"use client";

import { removeProposal } from "@/src/actions/proposals";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import ContentWrapper from "@/src/components/content-wrapper";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/src/components/ui/sheet";
import { Column } from "@/src/components/ui/table-grid";
import { proposalSituations, serviceTypeOptions } from "@/src/constants";
import { useToast } from "@/src/hooks/use-toast";
import { formatCurrency, formatDate } from "@/src/lib/utils";
import { Customer } from "@/src/types/core/customer";
import {
  Proposal,

  ProposalSituation,
} from "@/src/types/core/proposal";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { ServicesScope } from "@/src/types/core/services-scope";
import { <PERSON><PERSON>en, Trash } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import ProposalForm from "./proposal-form";
import { loadProposalTemplates } from "@/src/actions/proposal-templates";
import ProposalsTable, { ProposalsTableRef } from "./proposals-table";
import { updateProposalsPositions } from "@/src/actions/proposals-positions";

export default function ProposalList() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [templates, setTemplates] = useState<ProposalTemplateInterface[]>([]);
  const [sheetOpen, setSheetOpen] = useState(false);
  const [checkBeforeClose, setCheckBeforeClose] = useState<(() => void) | null>(null);
  const [proposal, setProposal] = useState<Proposal | undefined>(undefined);
  const [serviceScopes, setServiceScopes] = useState<ServicesScope[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentSearch, setCurrentSearch] = useState("");
  const [currentSituationFilter, setCurrentSituationFilter] = useState<string | null>(null);
  const [editingProposalId, setEditingProposalId] = useState<string | null>(null);
  // const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const tableRef = useRef<ProposalsTableRef>(null);

  // Função para atualizar o status de uma proposta
  const handleStatusChange = async (proposalId: string, newStatus: string) => {
    try {
      // setIsUpdatingStatus(true);

      // Encontrar a proposta no estado atual
      if (!proposal) {
        throw new Error("Proposta não encontrada");
      }

      // Atualizar o status da proposta
      const updatedProposal = {
        ...proposal,
        id: proposalId,
        situation: newStatus as ProposalSituation
      };

      // Enviar a atualização para o servidor
      await updateProposalsPositions([updatedProposal]);

      // Fechar o Sheet após a atualização
      setSheetOpen(false);

      // Atualizar a tabela mantendo a página e filtros atuais
      if (tableRef.current) {
        const tableState = tableRef.current.getTableState();
        tableRef.current.refresh(tableState.page, tableState.search, tableState.situationFilter);
      }

      // Mostrar mensagem de sucesso
      toast({
        title: "Sucesso",
        description: "Status da proposta atualizado com sucesso!",
        variant: "default"
      });
    } catch (error) {
      console.error("Erro ao atualizar status da proposta:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao atualizar o status da proposta.",
        variant: "destructive"
      });
    } finally {
      // setIsUpdatingStatus(false);
    }
  };

  const handleProposalSheet = async (proposal?: Proposal) => {
    try {
      // Limpar o estado da proposta antes de qualquer operação
      // Isso garante que o formulário seja limpo ao abrir para cadastro
      setProposal(undefined);

      // Definir o ID da proposta que está sendo editada (apenas se for edição)
      if (proposal) {
        setEditingProposalId(proposal.id);
      } else {
        // Se for um novo cadastro, garantir que o ID seja limpo
        setEditingProposalId(null);
      }

      // Guardar o estado atual da tabela (página e filtros) antes de qualquer operação
      if (tableRef.current) {
        const tableState = tableRef.current.getTableState();
        setCurrentPage(tableState.page);
        setCurrentSearch(tableState.search);
        setCurrentSituationFilter(tableState.situationFilter);
      }

      // Carregar apenas os dados necessários para o formulário, sem afetar a tabela
      const [customersData, templatesData, scopesData] = await Promise.all([
        fetchCustomers(),
        fetchTemplates(),
        fetchServiceScopes(),
      ]);

      // Atualizar os estados com os dados carregados
      setCustomers(customersData);
      setTemplates(templatesData);
      setServiceScopes(scopesData);

      // Abrir o formulário e definir a proposta (apenas se for edição)
      // Isso garante que o formulário seja aberto com os campos limpos para cadastro
      if (proposal) {
        setProposal(proposal);
      }

      setSheetOpen(true);
    } catch (error) {
      console.error("Erro ao carregar dados para o formulário:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os dados. Tente novamente.",
        variant: "destructive"
      });

      // Limpar o ID da proposta em caso de erro
      setEditingProposalId(null);
    }
  };

  const fetchCustomers = async () => {
    try {
      const res = await fetch("/api/customers?pageSize=1000"); // Aumentar o tamanho da página para obter todos os clientes
      if (!res.ok) throw new Error("Erro ao carregar clientes");

      const data = await res.json();
      console.log("Clientes carregados:", data);
      return data.data || []; // Acessar a propriedade 'data' que contém a lista de clientes
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      return [];
    }
  };

  const fetchTemplates = async () => {
    try {
      const result = await loadProposalTemplates(false, 1, 1000); // Carregar todos os templates
      console.log("Templates carregados:", result);
      return result?.data || []; // Acessar a propriedade 'data' que contém a lista de templates
    } catch (error) {
      console.error("Erro ao carregar templates:", error);
      return [];
    }
  };

  const fetchServiceScopes = async () => {
    try {
      const params = new URLSearchParams();
      ["PROPOSAL_SERVICE"].forEach((type) => params.append("type", type));
      params.append("pageSize", "1000"); // Aumentar o tamanho da página para obter todos os escopos

      const res = await fetch(`/api/service-scopes?${params.toString()}`);
      if (!res.ok) throw new Error("Erro ao carregar escopo de serviços");

      const data = await res.json();
      console.log("Escopos carregados:", data);
      return data.data || []; // Acessar a propriedade 'data' que contém a lista de escopos
    } catch (error) {
      console.error("Erro ao carregar escopo de serviços:", error);
      return [];
    }
  };



  // Variável para armazenar a URL atual
  // const [currentUrl, setCurrentUrl] = useState<string>("");

  // Função para extrair parâmetros da URL
  // const extractParamsFromUrl = (url: string) => {
  //   try {
  //     // Extrair a parte da query string da URL
  //     const queryString = url.split('?')[1];
  //     if (!queryString) return { page: 1, pageSize: 10, search: "", situationFilter: null };

  //     // Criar um objeto URLSearchParams para facilitar a extração dos parâmetros
  //     const params = new URLSearchParams(queryString);

  //     // Extrair os parâmetros relevantes
  //     const page = parseInt(params.get('page') || '1', 10);
  //     const pageSize = parseInt(params.get('pageSize') || '10', 10);
  //     const search = params.get('search') || '';
  //     const situationFilter = params.get('situation') || null;

  //     return { page, pageSize, search, situationFilter };
  //   } catch (error) {
  //     console.error("Erro ao extrair parâmetros da URL:", error);
  //     return { page: 1, pageSize: 10, search: "", situationFilter: null };
  //   }
  // };

  // Função para capturar a URL atual
  // const captureCurrentUrl = () => {
  //   if (typeof window !== 'undefined') {
  //     // Capturar a URL completa da requisição atual
  //     const url = window.location.href;
  //     console.log("URL atual capturada:", url);
  //     setCurrentUrl(url);
  //     return url;
  //   }
  //   return "";
  // };

  // Função para obter o estado atual da tabela
  const getCurrentTableState = () => {
    // Primeiro, tentar obter o estado da tabela diretamente
    if (tableRef.current) {
      try {
        const state = tableRef.current.getTableState();
        console.log("Estado obtido diretamente da tabela:", state);
        return state;
      } catch (error) {
        console.error("Erro ao obter estado da tabela:", error);
      }
    }

    // Se não conseguir obter da tabela, usar o estado atual
    return { page: currentPage, search: currentSearch, situationFilter: currentSituationFilter };
  };

  // Função para obter a página atual da URL da API salva no localStorage
  const getPageFromUrl = () => {
    if (typeof window !== 'undefined') {
      try {
        // Obter a URL da API salva no localStorage
        const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
        if (savedApiUrl) {
          // Extrair a página da URL da API
          const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
          const url = new URL(fullUrl);
          const pageParam = url.searchParams.get('page');
          if (pageParam) {
            console.log("Página obtida da URL da API salva no localStorage:", pageParam);
            return parseInt(pageParam, 10);
          }
        }

        // Obter a URL atual
        const url = new URL(window.location.href);

        // Se não encontrou nas requisições de API, tentar obter da URL atual
        const pageParam = url.searchParams.get('page');
        if (pageParam) {
          console.log("Página obtida da URL atual:", pageParam);
          return parseInt(pageParam, 10);
        }
      } catch (error) {
        console.error("Erro ao obter página da URL:", error);
      }
    }
    return currentPage || 1;
  };

  // Função para obter os filtros de situação da URL da API salva no localStorage
  const getSituationFiltersFromUrl = () => {
    if (typeof window !== 'undefined') {
      try {
        // Obter a URL da API salva no localStorage
        const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
        if (savedApiUrl) {
          // Extrair os filtros de situação da URL da API
          const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
          const url = new URL(fullUrl);
          const situations = url.searchParams.getAll('situation');
          if (situations.length > 0) {
            console.log("Filtros de situação obtidos da URL da API salva no localStorage:", situations);
            return situations.join(',');
          }
        }

        // Obter a URL atual
        const url = new URL(window.location.href);

        // Se não encontrou nas requisições de API, tentar obter da URL atual
        const situations = url.searchParams.getAll('situation');
        if (situations.length > 0) {
          console.log("Filtros de situação obtidos da URL atual:", situations);
          return situations.join(',');
        }
      } catch (error) {
        console.error("Erro ao obter filtros de situação da URL:", error);
      }
    }
    return currentSituationFilter;
  };

  // Variável para armazenar a página e filtros antes da exclusão
  // const [preDeleteState, setPreDeleteState] = useState<{
  //   page: number;
  //   situationFilter: string | null;
  //   search: string;
  // }>({ page: 1, situationFilter: null, search: "" });

  // Função para obter o termo de busca da URL da API salva no localStorage
  const getSearchFromUrl = () => {
    if (typeof window !== 'undefined') {
      try {
        // Obter a URL da API salva no localStorage
        const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
        if (savedApiUrl) {
          // Extrair o termo de busca da URL da API
          const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
          const url = new URL(fullUrl);
          const search = url.searchParams.get('search');
          if (search) {
            console.log("Termo de busca obtido da URL da API salva no localStorage:", search);
            return search;
          }
        }

        // Obter a URL atual
        const url = new URL(window.location.href);

        // Tentar obter o termo de busca da URL atual
        const search = url.searchParams.get('search');
        if (search) {
          console.log("Termo de busca obtido da URL atual:", search);
          return search;
        }
      } catch (error) {
        console.error("Erro ao obter termo de busca:", error);
      }
    }
    return currentSearch;
  };

  // Função para salvar o estado atual antes da exclusão
  const savePreDeleteState = () => {
    // Obter a página atual diretamente da URL ou do histórico de requisições
    const currentUrlPage = getPageFromUrl();
    const currentUrlSituationFilter = getSituationFiltersFromUrl();
    const currentUrlSearch = getSearchFromUrl();

    // Obter o estado atual da tabela como backup
    const tableState = getCurrentTableState();

    // Usar a página da URL se disponível, caso contrário usar a página do estado da tabela
    const pageToUse = currentUrlPage || tableState.page;
    const situationFilterToUse = currentUrlSituationFilter || tableState.situationFilter;
    const searchToUse = currentUrlSearch || tableState.search;

    // Salvar o estado atual
    const stateToSave = {
      page: pageToUse,
      situationFilter: situationFilterToUse,
      search: searchToUse
    };

    console.log("Salvando estado pré-exclusão:", stateToSave);
    // setPreDeleteState(stateToSave);

    return stateToSave;
  };

  const deleteProposal = async (id: string) => {
    try {
      // Obter o estado atual da tabela
      const savedState = savePreDeleteState();
      console.log("Estado obtido da tabela antes da exclusão:", savedState);

      // A flag de exclusão já foi definida quando o diálogo foi aberto
      // Verificar se a flag está definida
      if (typeof window !== 'undefined') {
        const isDeleting = localStorage.getItem('isProposalDeleting');
        if (!isDeleting) {
          console.log("Flag de exclusão não encontrada, definindo como true");
          localStorage.setItem('isProposalDeleting', 'true');
        } else {
          console.log("Flag de exclusão já está definida como:", isDeleting);
        }
      }

      setLoading(true);
      const data = await removeProposal(id);

      if (data) {
        toast({
          title: "Sucesso",
          description: data.message || "Proposta excluída com sucesso!",
          variant: "default"
        });

        // Pequeno atraso para garantir que a exclusão foi processada
        setTimeout(() => {
          // Atualizar a tabela mantendo a página e filtros salvos
          if (tableRef.current) {
            console.log("Atualizando tabela após exclusão com estado salvo:", savedState);

            // Definir explicitamente os estados para garantir que sejam mantidos
            setCurrentPage(savedState.page);
            setCurrentSituationFilter(savedState.situationFilter);
            setCurrentSearch(savedState.search);

            // Usar a URL da API salva no localStorage para atualizar a tabela
            if (typeof window !== 'undefined') {
              const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
              if (savedApiUrl) {
                console.log("Usando URL da API salva no localStorage para atualizar a tabela:", savedApiUrl);

                // Usar o método refreshWithApiUrl para atualizar a tabela com a URL da API
                tableRef.current.refreshWithApiUrl(savedApiUrl);

                // Não remover a URL da API do localStorage para permitir que o componente seja remontado com a mesma página
                console.log("URL da API mantida no localStorage para permitir remontagem do componente com a mesma página");

                // Limpar a flag de exclusão após a exclusão
                localStorage.removeItem('isProposalDeleting');
                console.log("Flag de exclusão removida após a exclusão");
              } else {
                // Fallback: usar os valores do estado salvo
                console.log("URL da API não encontrada no localStorage, usando valores do estado salvo");
                tableRef.current.refresh(
                  savedState.page,
                  savedState.search,
                  savedState.situationFilter
                );

                // Limpar a flag de exclusão mesmo quando não há URL da API salva
                localStorage.removeItem('isProposalDeleting');
                console.log("Flag de exclusão removida (fallback sem URL da API)");
              }
            } else {
              // Fallback: usar os valores do estado salvo
              tableRef.current.refresh(
                savedState.page,
                savedState.search,
                savedState.situationFilter
              );

              // Limpar a flag de exclusão mesmo quando não há referência à tabela
              if (typeof window !== 'undefined') {
                localStorage.removeItem('isProposalDeleting');
                console.log("Flag de exclusão removida (fallback sem referência à tabela)");
              }
            }
          }
          setLoading(false);
        }, 300);
      } else {
        // Limpar a flag de exclusão quando não há dados retornados
        if (typeof window !== 'undefined') {
          localStorage.removeItem('isProposalDeleting');
          console.log("Flag de exclusão removida (sem dados retornados)");
        }

        setLoading(false);
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao excluir proposta",
        variant: "destructive"
      });

      // Limpar a flag de exclusão em caso de erro
      if (typeof window !== 'undefined') {
        localStorage.removeItem('isProposalDeleting');
        console.log("Flag de exclusão removida (erro na exclusão)");
      }

      setLoading(false);
    }
  };

  // Usando função normal em vez de useCallback para evitar problemas
  async function loadData() {
    setLoading(true);
    try {
      const [customersData, templatesData, scopesData] = await Promise.all([
        fetchCustomers(),
        fetchTemplates(),
        fetchServiceScopes(),
      ]);

      setCustomers(customersData || []);
      setTemplates(templatesData || []);
      setServiceScopes(scopesData || []);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  }



  // Carregar dados quando o componente for montado
  useEffect(() => {
    loadData();
  }, []);

  const columns: Column<Proposal>[] = [
    {
      key: "customer",
      header: "Cliente",
      cell: (row) => (row.customer as Customer).name,
      sortable: true,
    },
    {
      key: "name",
      header: "Projeto",
      sortable: true,
    },
    {
      key: "budget",
      header: "Orçamento",
      cell: (row) => formatCurrency(row.budget),
      sortable: true,
    },
    {
      key: "startDate",
      header: "Data início",
      cell: (row) => formatDate(row.startDate, "DATE"),
      sortable: true,
    },
    {
      key: "endDate",
      header: "Data de conclusão prevista",
      cell: (row) => formatDate(row.endDate, "DATE"),
      sortable: true,
    },
    {
      key: "serviceType",
      header: "Tipo de Serviço",
      cell: (row) => {
        const serviceTypeLabel = serviceTypeOptions.find(
          (option) => option.value === row.serviceType
        )?.label || row.serviceType || '';

        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
            {serviceTypeLabel}
          </span>
        );
      },
      sortable: true,
    },
    {
      key: "situation",
      header: "Situação",
      cell: (row) => {
        const rowValue = row.situation as ProposalSituation;
        const situationLabel = proposalSituations.find(
          (situation) => situation.value == rowValue
        )?.label || '';

        // Determinar a classe do badge com base na situação
        let badgeClass = '';

        switch (rowValue) {
          case 'NEW':
            badgeClass = 'status-badge-new';
            break;
          case 'UNDER_ANALYSIS':
            badgeClass = 'status-badge-analysis';
            break;
          case 'PROPOSAL_SENT':
            badgeClass = 'status-badge-sent';
            break;
          case 'PROPOSAL_ACCEPTED':
            badgeClass = 'status-badge-accepted';
            break;
          case 'SIGN_REQUESTED':
            badgeClass = 'status-badge-sign';
            break;
          case 'SIGNED':
            badgeClass = 'status-badge-signed';
            break;
          case 'PROJECT_IN_PROGRESS':
            badgeClass = 'status-badge-in-progress';
            break;
          case 'PROJECT_FINISHED':
            badgeClass = 'status-badge-finished';
            break;
          case 'LOST':
            badgeClass = 'status-badge-lost';
            break;
          default:
            badgeClass = '';
        }

        return (
          <span className={`status-badge ${badgeClass}`}>
            {situationLabel}
          </span>
        );
      },
      sortable: true,
      filterable: true,
      filterOptions: proposalSituations
        .filter(s => ['NEW', 'UNDER_ANALYSIS', 'PROPOSAL_SENT', 'PROPOSAL_ACCEPTED'].includes(s.value))
        .map((s) => ({
          label: s.label,
          value: s.value,
        })),
      getFilterValue: (row) => row.situation as string,
      filterType: 'server', // Indica que o filtro deve ser processado no servidor
    },
    {
      key: "actions",
      header: "Ações",
      cell: (row) => (
        <div className="flex gap-3">
          {editingProposalId === row.id ? (
            <div className="size-5 flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
            </div>
          ) : (
            <SquarePen
              className="size-5 text-green-500 cursor-pointer"
              onClick={() => handleProposalSheet(row)}
            />
          )}
          <AppConfirmationDialog
            title="Deseja realmente excluir proposta?"
            description="Esta operação não poderá ser desfeita, todos os dados associados a proposta serão apagados."
            onConfirmCallback={() => {
              // Excluir a proposta
              // A função deleteProposal usa a URL da API salva no localStorage
              deleteProposal(row.id);
            }}
            onOpenChange={(open) => {
              // Quando o diálogo for aberto, salvar o estado atual
              if (open) {
                // Obter o estado atual da tabela
                const savedState = savePreDeleteState();
                console.log("Estado obtido da tabela ao abrir o diálogo:", savedState);

                // Salvar os valores no localStorage imediatamente
                if (typeof window !== 'undefined') {
                  // Definir a flag de exclusão quando o diálogo é aberto
                  localStorage.setItem('isProposalDeleting', 'true');
                  console.log("Flag de exclusão definida como true (diálogo aberto)");

                  // Verificar se há uma URL da API salva no localStorage
                  const savedApiUrl = localStorage.getItem('lastProposalApiUrl');

                  if (savedApiUrl) {
                    console.log("URL da API obtida do localStorage ao abrir o diálogo:", savedApiUrl);
                    try {
                      // Extrair parâmetros da URL da API
                      const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
                      const url = new URL(fullUrl);

                      // Extrair parâmetros da URL da API para debug
                      const pageParam = url.searchParams.get('page');
                      const pageSizeParam = url.searchParams.get('pageSize');
                      const situationParams = url.searchParams.getAll('situation');
                      const searchParam = url.searchParams.get('search');

                      console.log("Parâmetros extraídos da URL da API ao abrir o diálogo:", {
                        page: pageParam,
                        pageSize: pageSizeParam,
                        situations: situationParams,
                        search: searchParam
                      });
                    } catch (error) {
                      console.error("Erro ao extrair parâmetros da URL da API:", error);
                    }
                  } else {
                    // Se não há URL da API salva, usar os valores do estado atual
                    console.log("Nenhuma URL da API encontrada, usando valores do estado atual");

                    // Nada a fazer aqui, já que estamos usando a URL da API salva no localStorage
                  }

                  console.log("Diálogo aberto: URL da API salva no localStorage:", localStorage.getItem('lastProposalApiUrl'));
                }
              } else {
                // Quando o diálogo for fechado (sem confirmar a exclusão), manter a página atual
                // Isso é necessário porque o fechamento do diálogo pode causar uma re-renderização
                // que faz com que a tabela volte para a página 1

                // Limpar a flag de exclusão quando o diálogo é fechado sem confirmar
                if (typeof window !== 'undefined') {
                  localStorage.removeItem('isProposalDeleting');
                  console.log("Flag de exclusão removida (diálogo fechado sem confirmar)");
                }

                setTimeout(() => {
                  if (tableRef.current) {
                    // Verificar se há uma URL da API salva no localStorage
                    if (typeof window !== 'undefined') {
                      const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
                      if (savedApiUrl) {
                        console.log("Dialog fechado: URL da API obtida do localStorage:", savedApiUrl);
                        // Usar o método refreshWithApiUrl para atualizar a tabela com a URL da API
                        tableRef.current.refreshWithApiUrl(savedApiUrl);
                        return;
                      }
                    }

                    // Se não conseguir obter os parâmetros da URL da API, usar o estado atual da tabela
                    const tableState = tableRef.current.getTableState();
                    tableRef.current.refresh(tableState.page, tableState.search, tableState.situationFilter);

                    // Não precisamos limpar nada aqui, pois estamos usando diretamente a URL da API
                  }
                }, 100);
              }
            }}
            dialogCancelClassName="bg-transparent hover:bg-background/80"
            dialogActionClassName="bg-destructive hover:bg-destructive/90"
          >
            <Trash className="size-5 cursor-pointer" color="#ef4444" />
          </AppConfirmationDialog>
        </div>
      ),
    },
  ];

  return (
    <ContentWrapper title="Propostas" loading={loading}>
      <ProposalsTable
        ref={tableRef}
        columns={columns}
        situations={["NEW", "UNDER_ANALYSIS", "PROPOSAL_SENT", "PROPOSAL_ACCEPTED"]}
        serviceTypes={["CONSULTANCY"]}
        onAddClick={() => {
          handleProposalSheet(undefined);
          setSheetOpen(true);
        }}
        onPageChange={(page) => setCurrentPage(page)}
      />
      <Sheet
        open={sheetOpen}
        onOpenChange={(val) => {
          // Se estiver fechando o form (val = false)
          if (!val) {
            // Verificar se há alterações não salvas
            if (checkBeforeClose) {
              // Executar a função de verificação
              checkBeforeClose();
            } else {
              // Se não houver função de verificação, fechar normalmente
              setSheetOpen(false);

              // Limpar o ID da proposta
              setEditingProposalId(null);

              // Atualizar a tabela com os filtros anteriores
              tableRef.current?.refresh(currentPage, currentSearch, currentSituationFilter);
            }
          }
        }}
      >
        <SheetContent className="w-[90%] sm:min-w-[450px] thin-scrollbar">
          <SheetHeader>
            <SheetTitle />
            <SheetDescription />
          </SheetHeader>
          <ProposalForm
            scopes={serviceScopes}
            proposal={proposal}
            templates={templates}
            customers={customers}
            onChange={() => {
              // Limpar o ID da proposta
              setEditingProposalId(null);

              // Atualizar a tabela mantendo a página e filtros atuais
              if (tableRef.current) {
                const tableState = tableRef.current.getTableState();
                tableRef.current.refresh(tableState.page, tableState.search, tableState.situationFilter);
              }
            }}
            onCancelClick={() => {
              setSheetOpen(false);

              // Limpar o ID da proposta
              setEditingProposalId(null);

              // Atualizar a tabela ao fechar o formulário com os filtros anteriores
              tableRef.current?.refresh(currentPage, currentSearch, currentSituationFilter);
            }}
            onStatusChange={handleStatusChange}
            isEditing={!!proposal}
            onSheetCloseAttempt={(checkFn: () => void) => {
              // Armazenar a função de verificação
              setCheckBeforeClose(() => checkFn);
            }}
          />
        </SheetContent>
      </Sheet>
    </ContentWrapper>
  );
}

